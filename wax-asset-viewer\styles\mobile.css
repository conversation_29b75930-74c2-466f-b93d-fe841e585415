/* Mobile-First Responsive Styles */

/* Mobile Styles (default) */
@media screen and (max-width: 768px) {
    .app-container {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .logo-section {
        text-align: center;
    }

    .app-title {
        font-size: 1.5rem;
    }

    .endpoint-selector {
        justify-content: center;
    }

    .endpoint-dropdown {
        flex: 1;
        max-width: 250px;
    }

    /* Login Section Mobile */
    .login-container {
        padding: 30px 20px;
        margin: 0 10px;
    }

    .login-title {
        font-size: 1.3rem;
    }

    .login-button {
        width: 100%;
        justify-content: center;
        padding: 18px 20px;
    }

    /* Schema Section Mobile */
    .schema-container {
        padding: 20px 15px;
    }

    .schema-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .schema-btn {
        padding: 18px 15px;
        flex-direction: row;
        justify-content: center;
        text-align: left;
    }

    .schema-btn i {
        font-size: 1.3rem;
    }

    /* Gallery Mobile */
    .gallery-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .gallery-title {
        text-align: center;
        font-size: 1.2rem;
    }

    .gallery-controls {
        justify-content: center;
    }

    /* Featured Asset Mobile */
    .featured-asset {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px 15px;
    }

    .featured-image {
        max-height: 300px;
    }

    .featured-controls {
        justify-content: center;
    }

    .nav-btn {
        padding: 15px 18px;
    }

    /* Thumbnail Gallery Mobile */
    .thumbnail-gallery {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 10px;
        padding: 15px 0;
    }

    .thumbnail-gallery.size-small {
        grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    }

    .thumbnail-gallery.size-small .thumbnail-image {
        height: 70px;
    }

    .thumbnail-gallery.size-medium {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .thumbnail-gallery.size-medium .thumbnail-image {
        height: 120px;
    }

    .thumbnail-gallery.size-large {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .thumbnail-gallery.size-large .thumbnail-image {
        height: 150px;
    }

    .thumbnail-image {
        height: 100px;
    }

    .thumbnail-info {
        font-size: 0.7rem;
        padding: 8px 6px 6px;
    }
}

/* Small Mobile Styles */
@media screen and (max-width: 480px) {
    .app-container {
        padding: 10px;
    }

    .app-title {
        font-size: 1.3rem;
    }

    .endpoint-dropdown {
        font-size: 0.8rem;
        padding: 6px 10px;
    }

    .rotate-btn {
        padding: 6px 8px;
    }

    /* Login Mobile Small */
    .login-container {
        padding: 25px 15px;
    }

    .login-title {
        font-size: 1.2rem;
    }

    .login-description {
        font-size: 0.9rem;
    }

    /* Schema Mobile Small */
    .schema-container {
        padding: 15px 10px;
    }

    .schema-btn {
        padding: 15px 12px;
        font-size: 0.9rem;
    }

    /* Featured Asset Mobile Small */
    .featured-asset {
        padding: 15px 10px;
    }

    .featured-name {
        font-size: 1.1rem;
    }

    .featured-details {
        font-size: 0.9rem;
    }

    /* Thumbnail Gallery Mobile Small */
    .thumbnail-gallery {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 8px;
    }

    .thumbnail-gallery.size-small {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    }

    .thumbnail-gallery.size-small .thumbnail-image {
        height: 60px;
    }

    .thumbnail-gallery.size-medium {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .thumbnail-gallery.size-medium .thumbnail-image {
        height: 100px;
    }

    .thumbnail-gallery.size-large {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .thumbnail-gallery.size-large .thumbnail-image {
        height: 120px;
    }

    .thumbnail-image {
        height: 80px;
    }

    .thumbnail-info {
        font-size: 0.65rem;
        padding: 6px 4px 4px;
    }
}

/* Tablet Styles */
@media screen and (min-width: 769px) and (max-width: 1024px) {
    .app-container {
        padding: 25px;
    }

    .featured-asset {
        grid-template-columns: 1fr 1fr;
        gap: 25px;
    }

    .thumbnail-gallery {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }

    .thumbnail-gallery.size-small {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .thumbnail-gallery.size-medium {
        grid-template-columns: repeat(auto-fill, minmax(170px, 1fr));
    }

    .thumbnail-gallery.size-large {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }
}

/* Large Desktop Styles */
@media screen and (min-width: 1400px) {
    .app-container {
        padding: 30px;
    }

    .thumbnail-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 20px;
    }

    .thumbnail-gallery.size-large {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .schema-btn,
    .login-button,
    .control-btn,
    .nav-btn,
    .rotate-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .thumbnail-item {
        min-height: 44px;
    }
}

/* Landscape orientation adjustments */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .featured-asset {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .featured-image {
        max-height: 250px;
    }

    .login-section {
        min-height: 50vh;
    }
}
