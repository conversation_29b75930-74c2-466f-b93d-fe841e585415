<!DOCTYPE html>
<html>
<head><meta charset="us-ascii"><meta name="viewport" content="width=device-width">
	<title>Harriet&#39;s <PERSON><PERSON> Viewer</title>
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.1/css/all.css" integrity="sha384-vp86vTRFVJgpjF9jiIGPEEqYqlDwgyBgEF109VFjmqGmIY/Y4HV4d3Gp2irVfcrp" crossorigin="anonymous">
	<link href="style.css" rel="stylesheet" type="text/css" /> 
	<link href="images/ht.png" rel="icon" type="image/png" />
	<link href="https://fonts.cdnfonts.com/css/common-pixel" rel="stylesheet" />
	<link href="https://fonts.cdnfonts.com/css/space-mono" rel="stylesheet" />
	<link href="https://fonts.cdnfonts.com/css/common-pixel" rel="stylesheet" />
	<link href="https://fonts.cdnfonts.com/css/space-mono" rel="stylesheet" /><script src="https://unpkg.com/axios@1.0.0/dist/axios.min.js">
  </script><script src="waxjs.js">
  </script><script src="https://code.jquery.com/jquery-3.6.1.slim.js"
    integrity="sha256-tXm+sa1uzsbFnbXt8GJqsgi2Tw+m4BLGDof6eUPjbtk=" crossorigin="anonymous"></script><script src="index.js"></script><script src="login.js"></script>
</head>
<body> 
<div id="harriets" class="container scanlines" >
<div class="logo" id="logotext"><img src="images/ht.png"> Harriet's Teas Viewer</div> 
<div class="menu">
  <button class="btn green_btn" id="login_btn" onclick="login()"><i class="fas fa-sign-in-alt"></i> Login</button>

<button class="btn green_btn" onclick="window.open('https://wax.atomichub.io/market?collection_name=harrietsteas&order=asc&sort=price&symbol=WAX');"><i class="fas fa-shopping-cart"></i> Shop</button>

<button class="btn green_btn" onclick="changeNFT('back', myNFTS)"><i class="fas fa-arrow-left"></i></button>

<button class="btn green_btn" onclick="changeNFT('next', myNFTS)"><i class="fas fa-arrow-right"></i></button>

<button class="btn green_btn" onclick="clearCurrentImage()"><i class="fas fa-times"></i> Clear</button>

<button class="btn green_btn" onclick="changeThumbnailSize()"><i class="fas fa-search-plus"></i> Thumbnails</button>

<button class="btn green_btn" onclick="showPlayer()"><i class="fas fa-volume-up"></i> Play</button>
<div  class="custom-select" style="width:200px;">
  <select id="collectionSelected" onchange="setDisplayCollection()">
    <option value="0">Select Collection</option> 
    <option value="1">Harriet's Teas</option>  
    <option value="2">Pseudai - Research</option>  
    <option value="3">Pseudai - Series</option>
  </select></div>

</div></div>
<div class="action_text">
<span id="action_h1"></span> 
<p id="action_p"></p> 
<p id="action_thumbs"></p></div>  
</div>
  <footer class="footer">
    <div class="footer-content"><div id="player"><iframe id="player" type="text/html" width="300" height="180"
  src="https://www.youtube.com/embed/M8aiQhz5hM0?controls=0"
  frameborder="0"></iframe></div>
      © Harriet's Teas 2023
    </div>
  </footer>
  
<script>  const wax = new waxjs.WaxJS({
      rpcEndpoint: 'https://wax.greymass.com'
    });  
   $("#collectionSelected")[0].selectedIndex = 0; 
document.onkeydown = checkKey; 
function checkKey(e) { 
    e = e || window.event; 
    if (e.keyCode == '38') { 
           }
    else if (e.keyCode == '40') { 
    }
    else if (e.keyCode == '37') { 
      changeNFT('back', myNFTS)
    }
    else if (e.keyCode == '39') { 
      changeNFT('next', myNFTS)
    } 
} 
  $('#collectionSelected').hide();
  $('#player').hide(); 
</script><script src="script.js"> </script>

</body>
</html>