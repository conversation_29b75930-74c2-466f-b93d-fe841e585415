# WAX Asset Viewer

A mobile-first responsive web application for viewing WAX blockchain NFT assets with a sleek neon green cyberpunk aesthetic.

## Features

- **Mobile-First Design**: Optimized for mobile devices with responsive layout
- **WAX Wallet Integration**: Connect your WAX wallet to view your NFTs
- **Multiple Collections**: Support for Harriet's Teas and Pseudai collections
- **Endpoint Rotation**: Automatic failover between multiple API endpoints
- **Gallery View**: Beautiful thumbnail gallery with featured asset display
- **Keyboard Navigation**: Arrow keys for navigation, spacebar for thumbnail size toggle
- **Neon Green Theme**: Cyberpunk-inspired design with glowing effects

## File Structure

```
wax-asset-viewer/
├── index.html              # Main HTML file
├── styles/
│   ├── main.css            # Main styles with neon green theme
│   └── mobile.css          # Mobile-responsive styles
├── js/
│   ├── config.js           # Configuration and state management
│   ├── api.js              # WAX API integration with endpoint rotation
│   ├── auth.js             # WAX wallet authentication
│   ├── gallery.js          # Asset gallery functionality
│   └── app.js              # Main application controller
└── README.md               # This file
```

## Usage

1. Open `index.html` in a web browser
2. Click "Connect Wallet" to connect your WAX wallet
3. Select a collection schema (Harriet's Teas, Pseudai Research, or Pseudai Series)
4. Browse your assets in the gallery view
5. Click thumbnails to view featured assets
6. Use arrow keys or navigation buttons to browse

## Keyboard Shortcuts

- **Arrow Left/Right**: Navigate between assets
- **Spacebar**: Toggle thumbnail size
- **Escape**: Clear gallery and return to schema selection
- **Ctrl+L**: Logout (development shortcut)

## API Endpoints

The app automatically rotates between multiple endpoints for reliability:

1. `https://wax.api.atomicassets.io` (Primary)
2. `https://api.wax-aa.bountyblok.io` (BountyBlok)
3. `https://wax.greymass.com` (Greymass)

## Mobile Optimization

- Touch-friendly button sizes (minimum 44px)
- Responsive grid layouts
- Optimized for portrait and landscape orientations
- Smooth animations and transitions
- Efficient image loading

## Browser Compatibility

- Modern browsers with ES5+ support
- Mobile Safari (iOS 10+)
- Chrome Mobile (Android 5+)
- Desktop browsers (Chrome, Firefox, Safari, Edge)

## Dependencies

- WAX JS SDK (included via ../waxjs.js)
- Font Awesome icons (CDN)
- Google Fonts (Orbitron)

## Development

The application uses vanilla JavaScript (no ES6 modules) for maximum compatibility. All code is modular and organized into separate files for maintainability.

## Styling

The app uses a cyberpunk-inspired design with:
- Neon green (#31c16a) primary color
- Bright neon green (#08fa0f) for highlights
- Dark backgrounds with subtle transparency
- Neuromorphic design elements
- Smooth animations and hover effects

## Error Handling

- Automatic endpoint rotation on API failures
- Graceful degradation when endpoints are unavailable
- User-friendly error messages
- Loading states for all async operations
