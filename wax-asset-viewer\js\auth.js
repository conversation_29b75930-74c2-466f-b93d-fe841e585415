// Authentication and WAX wallet integration
var WaxAuth = {
    wax: null,
    
    // Initialize WAX
    initialize: function() {
        try {
            this.wax = new waxjs.WaxJS({
                rpcEndpoint: 'https://wax.greymass.com'
            });
            console.log('WAX initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize WAX:', error);
            Utils.showError('Failed to initialize WAX connection');
            return false;
        }
    },
    
    // Login to WAX
    login: function() {
        var self = this;
        return new Promise(function(resolve, reject) {
            if (!self.wax) {
                if (!self.initialize()) {
                    reject(new Error('WAX not initialized'));
                    return;
                }
            }
            
            Utils.showLoading('Connecting to WAX wallet...');
            
            self.wax.login()
                .then(function(userAccount) {
                    console.log('WAX login successful:', userAccount);
                    AppState.setUser(userAccount);
                    self.updateConnectionStatus(true, userAccount);
                    Utils.hideLoading();
                    resolve(userAccount);
                })
                .catch(function(error) {
                    console.error('WAX login failed:', error);
                    Utils.hideLoading();
                    Utils.showError('Failed to connect to WAX wallet');
                    reject(error);
                });
        });
    },
    
    // Logout
    logout: function() {
        AppState.clearUser();
        this.updateConnectionStatus(false);
        
        // Reset UI to login state
        UI.showLoginSection();
        UI.hideMainContent();
        
        console.log('User logged out');
    },
    
    // Update connection status in UI
    updateConnectionStatus: function(connected, account) {
        var statusIndicator = document.querySelector('.status-indicator');
        var statusText = document.querySelector('.status-text');
        
        if (statusIndicator && statusText) {
            if (connected) {
                statusIndicator.classList.add('connected');
                statusText.textContent = 'Connected: ' + (account || 'Unknown');
            } else {
                statusIndicator.classList.remove('connected');
                statusText.textContent = 'Disconnected';
            }
        }
    },
    
    // Check if user is logged in
    isLoggedIn: function() {
        return AppState.user.isLoggedIn && AppState.user.account;
    },
    
    // Get current user account
    getCurrentUser: function() {
        return AppState.user.account;
    }
};

// UI Management for authentication
var AuthUI = {
    // Initialize auth UI
    initialize: function() {
        this.bindEvents();
        this.updateUI();
    },
    
    // Bind authentication events
    bindEvents: function() {
        var loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            loginBtn.addEventListener('click', this.handleLogin.bind(this));
        }
        
        // Add logout functionality (could be added to a menu later)
        document.addEventListener('keydown', function(e) {
            // Ctrl+L for logout (for development)
            if (e.ctrlKey && e.key === 'l') {
                e.preventDefault();
                WaxAuth.logout();
            }
        });
    },
    
    // Handle login button click
    handleLogin: function() {
        var self = this;
        
        WaxAuth.login()
            .then(function(userAccount) {
                console.log('Login successful for:', userAccount);
                self.onLoginSuccess(userAccount);
            })
            .catch(function(error) {
                console.error('Login failed:', error);
                self.onLoginError(error);
            });
    },
    
    // Handle successful login
    onLoginSuccess: function(userAccount) {
        // Hide login section
        Utils.hideElement('login-section');
        
        // Show main content
        Utils.showElement('main-content', 'fade-in');
        
        // Update app state
        AppState.ui.currentView = 'schema';
        
        // Initialize schema selection
        SchemaUI.initialize();
        
        console.log('User interface updated for logged in user');
    },
    
    // Handle login error
    onLoginError: function(error) {
        console.error('Login error:', error);
        
        // Could show error message in UI
        var loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            var originalText = loginBtn.innerHTML;
            loginBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Login Failed';
            loginBtn.style.borderColor = '#ff4444';
            loginBtn.style.color = '#ff4444';
            
            setTimeout(function() {
                loginBtn.innerHTML = originalText;
                loginBtn.style.borderColor = '';
                loginBtn.style.color = '';
            }, 3000);
        }
    },
    
    // Update UI based on auth state
    updateUI: function() {
        if (WaxAuth.isLoggedIn()) {
            Utils.hideElement('login-section');
            Utils.showElement('main-content');
            AppState.ui.currentView = 'schema';
        } else {
            Utils.showElement('login-section');
            Utils.hideElement('main-content');
            AppState.ui.currentView = 'login';
        }
    }
};

// Schema selection UI
var SchemaUI = {
    selectedSchema: null,
    
    // Initialize schema UI
    initialize: function() {
        this.bindEvents();
        this.updateUI();
    },
    
    // Bind schema selection events
    bindEvents: function() {
        var schemaBtns = document.querySelectorAll('.schema-btn');
        schemaBtns.forEach(function(btn) {
            btn.addEventListener('click', this.handleSchemaSelect.bind(this));
        }.bind(this));
    },
    
    // Handle schema selection
    handleSchemaSelect: function(event) {
        var btn = event.currentTarget;
        var collection = btn.getAttribute('data-collection');
        var schema = btn.getAttribute('data-schema');
        
        if (!collection || !schema) {
            console.error('Invalid schema button configuration');
            return;
        }
        
        // Update button states
        this.updateButtonStates(btn);
        
        // Load assets for selected schema
        this.loadSchemaAssets(collection, schema);
    },
    
    // Update button visual states
    updateButtonStates: function(activeBtn) {
        var schemaBtns = document.querySelectorAll('.schema-btn');
        schemaBtns.forEach(function(btn) {
            btn.classList.remove('active');
        });
        
        activeBtn.classList.add('active');
        this.selectedSchema = activeBtn;
    },
    
    // Load assets for selected schema
    loadSchemaAssets: function(collection, schema) {
        var user = WaxAuth.getCurrentUser();
        if (!user) {
            Utils.showError('No user logged in');
            return;
        }
        
        var self = this;
        
        EnhancedAPI.fetchAssetsWithRetry(collection, schema, user)
            .then(function(assets) {
                console.log('Loaded ' + assets.length + ' assets for ' + collection + '/' + schema);
                
                // Update app state
                AppState.setCollection(collection, schema, assets);
                
                // Show gallery
                self.showGallery(assets);
            })
            .catch(function(error) {
                console.error('Failed to load assets:', error);
                Utils.showError('Failed to load assets for ' + collection + '/' + schema);
            });
    },
    
    // Show gallery with loaded assets
    showGallery: function(assets) {
        // Update UI state
        AppState.ui.currentView = 'gallery';
        
        // Show gallery section
        Utils.showElement('gallery-section', 'fade-in');
        
        // Initialize gallery
        Gallery.initialize(assets);
        
        console.log('Gallery displayed with ' + assets.length + ' assets');
    },
    
    // Update schema UI
    updateUI: function() {
        // Could add logic to restore previous selection
    }
};
