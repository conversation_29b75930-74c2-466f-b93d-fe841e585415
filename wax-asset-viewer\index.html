<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WAX Asset Viewer - <PERSON>'s Collection</title>
    <link rel="icon" type="image/png" href="images/logo.png">
    <link rel="stylesheet" href="../styles.css">
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/mobile.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="loading-text">Connecting to WAX Network...</p>
        </div>
    </div>

    <!-- Main App Container -->
    <div id="app-container" class="app-container hidden">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-container">
                        <img src="images/logo.png" alt="Harriet's Logo" class="header-logo">
                        <h1 class="app-title">WAX Asset Viewer</h1>
                    </div>
                    <div class="connection-status" id="connection-status">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Disconnected</span>
                    </div>
                </div>
                
                <!-- Endpoint Selector -->
                <div class="endpoint-selector">
                    <select id="endpoint-select" class="endpoint-dropdown">
                        <option value="https://aa-wax-public1.neftyblocks.com">NeftyBlocks (Primary)</option>
                        <option value="https://atomic.3dkrender.com">3DKRender</option>
                        <option value="https://wax-aa.eu.eosamsterdam.net">EOSAmsterdam</option>
                        <option value="https://atomic-wax.a-dex.xyz">A-DEX BP</option>
                        <option value="https://wax-atomic.alcor.exchange">AlcorExchange</option>
                        <option value="https://atomic-wax-mainnet.wecan.dev">WeCan</option>
                        <option value="https://atomic-api.wax.cryptolions.io">CryptoLions🦁</option>
                        <option value="https://aa.dapplica.io">dapplica</option>
                        <option value="https://aa-api-wax.eosauthority.com">EOS Authority</option>
                        <option value="https://wax-aa.eosdac.io">eosDAC</option>
                        <option value="https://wax-atomic-api.eosphere.io">EOSphere Guild</option>
                        <option value="https://atomic.wax.eosrio.io">EOS Rio 💙</option>
                        <option value="https://atomic-wax.tacocrypto.io">Taco</option>
                        <option value="https://aa.waxdaobp.io">WaxDAO BP</option>
                        <option value="https://wax.eosusa.io">WAXUSA</option>
                        <option value="https://atomic-wax.qaraqol.com">QaraqolBlock</option>
                        <option value="https://api.waxeastern.cn">WAX.Eastern</option>
                        <option value="https://atomic.hivebp.io">Hive BP</option>
                        <option value="https://wax.api.atomicassets.io">AtomicAssets (Official)</option>
                        <option value="https://api.wax-aa.bountyblok.io">BountyBlok</option>
                        <option value="https://wax.greymass.com">Greymass</option>
                    </select>
                    <button id="rotate-endpoint" class="rotate-btn" title="Rotate Endpoint">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Login Section -->
        <section id="login-section" class="login-section">
            <div class="login-container neuromorphic-box">
                <div class="login-content">
                    <h2 class="login-title">Connect Your WAX Wallet</h2>
                    <p class="login-description">Access your NFT collection by connecting your WAX wallet</p>
                    <button id="login-btn" class="login-button">
                        <i class="fas fa-wallet"></i>
                        Connect Wallet
                    </button>
                </div>
            </div>
        </section>

        <!-- Main Content -->
        <main id="main-content" class="main-content hidden">
            <!-- Schema Selector -->
            <section class="schema-section">
                <div class="schema-container neuromorphic-box">
                    <h3 class="schema-title">Select Collection Schema</h3>
                    <div class="schema-grid">
                        <button class="schema-btn" data-collection="harrietsteas" data-schema="collectibles">
                            <i class="fas fa-leaf"></i>
                            <span>Harriet's Teas</span>
                        </button>
                        <button class="schema-btn" data-collection="pseudaimusic" data-schema="research">
                            <i class="fas fa-microscope"></i>
                            <span>Pseudai Research</span>
                        </button>
                        <button class="schema-btn" data-collection="pseudaimusic" data-schema="series">
                            <i class="fas fa-music"></i>
                            <span>Pseudai Series</span>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Asset Gallery -->
            <section id="gallery-section" class="gallery-section hidden">
                <div class="gallery-header">
                    <h3 class="gallery-title">Your Assets</h3>
                    <div class="gallery-controls">
                        <button id="blur-toggle-btn" class="blur-toggle-btn" title="Toggle Blur for Discretion">
                            <i class="fas fa-eye"></i>
                            <span>Blur</span>
                        </button>
                        <button id="thumbnail-size-btn" class="control-btn" title="Toggle Thumbnail Size">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button id="clear-gallery-btn" class="control-btn" title="Clear Gallery">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="asset-count">
                    <span id="asset-count-text">0 assets found</span>
                </div>

                <!-- Featured Asset Display -->
                <div id="featured-asset" class="featured-asset neuromorphic-box hidden">
                    <div class="featured-image-container">
                        <img id="featured-image" class="featured-image" alt="Featured Asset">
                    </div>
                    <div class="featured-info">
                        <h4 id="featured-name" class="featured-name"></h4>
                        <p id="featured-details" class="featured-details"></p>
                        <div class="featured-controls">
                            <button id="prev-asset" class="nav-btn">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button id="next-asset" class="nav-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Thumbnail Gallery -->
                <div id="thumbnail-gallery" class="thumbnail-gallery">
                    <!-- Thumbnails will be dynamically inserted here -->
                </div>
            </section>
        </main>
    </div>

    <!-- Full Screen Image Viewer -->
    <div id="fullscreen-viewer" class="fullscreen-viewer">
        <div class="fullscreen-content">
            <img id="fullscreen-image" class="fullscreen-image" alt="Full Screen View">
            <button id="fullscreen-prev" class="fullscreen-nav prev">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button id="fullscreen-next" class="fullscreen-nav next">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
        <div class="fullscreen-controls">
            <button id="fullscreen-close" class="fullscreen-close">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../waxjs.js"></script>
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/gallery.js"></script>
    <script src="js/fullscreen-viewer.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
