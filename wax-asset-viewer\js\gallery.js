// Gallery functionality for displaying assets
var Gallery = {
    assets: [],
    currentIndex: 0,
    thumbnailSize: 'medium',
    
    // Initialize gallery
    initialize: function(assets) {
        this.assets = assets || [];
        this.currentIndex = 0;
        this.bindEvents();
        this.render();
    },
    
    // Bind gallery events
    bindEvents: function() {
        // Navigation buttons
        var prevBtn = document.getElementById('prev-asset');
        var nextBtn = document.getElementById('next-asset');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', this.previousAsset.bind(this));
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', this.nextAsset.bind(this));
        }
        
        // Control buttons
        var thumbnailSizeBtn = document.getElementById('thumbnail-size-btn');
        var clearBtn = document.getElementById('clear-gallery-btn');
        
        if (thumbnailSizeBtn) {
            thumbnailSizeBtn.addEventListener('click', this.toggleThumbnailSize.bind(this));
        }
        
        if (clearBtn) {
            clearBtn.addEventListener('click', this.clearGallery.bind(this));
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboard.bind(this));
    },
    
    // Render gallery
    render: function() {
        this.updateAssetCount();
        this.renderFeaturedAsset();
        this.renderThumbnails();
    },
    
    // Update asset count display
    updateAssetCount: function() {
        var countElement = document.getElementById('asset-count-text');
        if (countElement) {
            var count = this.assets.length;
            var text = count + ' asset' + (count !== 1 ? 's' : '') + ' found';
            countElement.textContent = text;
        }
    },
    
    // Render featured asset
    renderFeaturedAsset: function() {
        var featuredSection = document.getElementById('featured-asset');
        var featuredImage = document.getElementById('featured-image');
        var featuredName = document.getElementById('featured-name');
        var featuredDetails = document.getElementById('featured-details');
        
        if (this.assets.length === 0) {
            if (featuredSection) {
                featuredSection.classList.add('hidden');
            }
            return;
        }
        
        var asset = this.assets[this.currentIndex];
        
        if (featuredSection) {
            featuredSection.classList.remove('hidden');
        }
        
        if (featuredImage && asset.data && asset.data.img) {
            featuredImage.src = Utils.getImageUrl(asset.data.img);
            featuredImage.alt = Utils.formatAssetName(asset);
        }
        
        if (featuredName) {
            featuredName.textContent = Utils.formatAssetName(asset);
        }
        
        if (featuredDetails) {
            var details = this.formatAssetDetails(asset);
            featuredDetails.textContent = details;
        }
    },
    
    // Format asset details
    formatAssetDetails: function(asset) {
        var details = [];
        
        if (asset.asset_id) {
            details.push('Asset ID: ' + asset.asset_id);
        }
        
        if (asset.template && asset.template.template_id) {
            details.push('Template: ' + asset.template.template_id);
        }
        
        if (asset.schema && asset.schema.schema_name) {
            details.push('Schema: ' + asset.schema.schema_name);
        }
        
        if (asset.collection && asset.collection.collection_name) {
            details.push('Collection: ' + asset.collection.collection_name);
        }
        
        return details.join(' • ');
    },
    
    // Render thumbnail gallery
    renderThumbnails: function() {
        var gallery = document.getElementById('thumbnail-gallery');
        if (!gallery) return;
        
        // Clear existing thumbnails
        gallery.innerHTML = '';
        
        // Set thumbnail size class
        gallery.className = 'thumbnail-gallery size-' + this.thumbnailSize;
        
        // Create thumbnails
        this.assets.forEach(function(asset, index) {
            var thumbnail = this.createThumbnail(asset, index);
            gallery.appendChild(thumbnail);
        }.bind(this));
        
        // Update active thumbnail
        this.updateActiveThumbnail();
    },
    
    // Create thumbnail element
    createThumbnail: function(asset, index) {
        var container = document.createElement('div');
        container.className = 'thumbnail-item';
        container.setAttribute('data-index', index);
        
        var img = document.createElement('img');
        img.className = 'thumbnail-image';
        img.alt = Utils.formatAssetName(asset);
        
        if (asset.data && asset.data.img) {
            img.src = Utils.getImageUrl(asset.data.img);
        }
        
        var info = document.createElement('div');
        info.className = 'thumbnail-info';
        info.textContent = asset.name || 'Unnamed';
        
        container.appendChild(img);
        container.appendChild(info);
        
        // Add click handler
        container.addEventListener('click', function() {
            this.selectAsset(index);
        }.bind(this));
        
        return container;
    },
    
    // Update active thumbnail
    updateActiveThumbnail: function() {
        var thumbnails = document.querySelectorAll('.thumbnail-item');
        thumbnails.forEach(function(thumb, index) {
            if (index === this.currentIndex) {
                thumb.classList.add('active');
            } else {
                thumb.classList.remove('active');
            }
        }.bind(this));
    },
    
    // Select asset by index
    selectAsset: function(index) {
        if (index >= 0 && index < this.assets.length) {
            this.currentIndex = index;
            AppState.setAssetIndex(index);
            this.renderFeaturedAsset();
            this.updateActiveThumbnail();
        }
    },
    
    // Navigate to next asset
    nextAsset: function() {
        if (this.assets.length === 0) return;
        
        this.currentIndex = (this.currentIndex + 1) % this.assets.length;
        AppState.nextAsset();
        this.renderFeaturedAsset();
        this.updateActiveThumbnail();
    },
    
    // Navigate to previous asset
    previousAsset: function() {
        if (this.assets.length === 0) return;
        
        this.currentIndex = this.currentIndex === 0 
            ? this.assets.length - 1 
            : this.currentIndex - 1;
        AppState.prevAsset();
        this.renderFeaturedAsset();
        this.updateActiveThumbnail();
    },
    
    // Toggle thumbnail size
    toggleThumbnailSize: function() {
        var sizes = AppConfig.ui.thumbnailSizes;
        var currentIndex = sizes.indexOf(this.thumbnailSize);
        var nextIndex = (currentIndex + 1) % sizes.length;
        
        this.thumbnailSize = sizes[nextIndex];
        AppState.ui.thumbnailSize = this.thumbnailSize;
        
        // Re-render thumbnails with new size
        this.renderThumbnails();
        
        console.log('Thumbnail size changed to:', this.thumbnailSize);
    },
    
    // Clear gallery
    clearGallery: function() {
        this.assets = [];
        this.currentIndex = 0;
        AppState.setCollection(null, null, []);
        
        // Hide gallery section
        Utils.hideElement('gallery-section');
        
        // Reset schema selection
        var schemaBtns = document.querySelectorAll('.schema-btn');
        schemaBtns.forEach(function(btn) {
            btn.classList.remove('active');
        });
        
        console.log('Gallery cleared');
    },
    
    // Handle keyboard navigation
    handleKeyboard: function(event) {
        if (AppState.ui.currentView !== 'gallery') return;
        
        switch(event.key) {
            case 'ArrowLeft':
                event.preventDefault();
                this.previousAsset();
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.nextAsset();
                break;
            case 'Escape':
                event.preventDefault();
                this.clearGallery();
                break;
            case ' ':
                event.preventDefault();
                this.toggleThumbnailSize();
                break;
        }
    }
};
