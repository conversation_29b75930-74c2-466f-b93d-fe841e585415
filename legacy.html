<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Viewer</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="styles.css">   
</head>
<body>

<div class="w-full h-full relative">
  <!-- Blurred Background -->
  <div id="blur-background" class="blur-background"></div>
  <div class="content">
    <img id="screen-image" class="screen-image" alt="Theatre Content">
  </div>
</div>
 
<div class="speech-container neuromorphic-box p-6">
  <p class="text-lg text-white font-semibold mb-4"> 
  </p> 
  <button onclick="changeImage()"> 
  </button>
</div>

<script> 
 
  updateBlurBackground(images[currentIndex]);

  function changeImage() {
    const imgElement = document.getElementById('screen-image');
    imgElement.classList.add('fade-out');
    setTimeout(() => {
      currentIndex = (currentIndex + 1) % images.length;
      imgElement.src = images[currentIndex];
      updateBlurBackground(images[currentIndex]); // Update the blurred background
      imgElement.classList.remove('fade-out');
    }, 500);
  }
</script>

</body>
</html>