// Initialize WAX
const wax = new waxjs.WaxJS({
    rpcEndpoint: 'https://wax.greymass.com'
});
// List of Atomic Assets API nodes fallbacks we should be able to switch between by clicking a rotate button that will select next in line and rotate all the way to the first if the user keeps clicking the rotate icon butotn
const atomicNodes = [
  { id: 17, name: '3DKRender', url: 'https://atomic.3dkrender.com' },
  { id: 9, name: 'EOSAmsterdam', url: 'https://wax-aa.eu.eosamsterdam.net' },
  { id: 44, name: 'A-DEX BP', url: 'https://atomic-wax.a-dex.xyz' },
  { id: 19, name: 'AlcorExchange', url: 'https://wax-atomic.alcor.exchange' },
  { id: 28, name: 'We<PERSON><PERSON>', url: 'https://atomic-wax-mainnet.wecan.dev' },
  { id: 24, name: 'CryptoLions🦁', url: 'https://atomic-api.wax.cryptolions.io' },
  { id: 12, name: 'dapplica', url: 'https://aa.dapplica.io' },
  { id: 15, name: 'EOS Authority', url: 'https://aa-api-wax.eosauthority.com' },
  { id: 7, name: 'eosDAC', url: 'https://wax-aa.eosdac.io' },
  { id: 4, name: 'EOSphere Guild', url: 'https://wax-atomic-api.eosphere.io' },
  { id: 8, name: 'EOS Rio 💙', url: 'https://atomic.wax.eosrio.io' },
  { id: 10, name: 'NeftyGuild', url: 'https://aa-wax-public1.neftyblocks.com' },
  { id: 16, name: 'Taco', url: 'https://atomic-wax.tacocrypto.io' },
  { id: 18, name: 'WaxDAO BP', url: 'https://aa.waxdaobp.io' },
  { id: 6, name: 'WAXUSA', url: 'https://wax.eosusa.io' },
  { id: 42, name: 'QaraqolBlock', url: 'https://atomic-wax.qaraqol.com' },
  { id: 27, name: 'WAX.Eastern', url: 'https://api.waxeastern.cn' },
  { id: 11, name: 'Hive BP', url: 'https://atomic.hivebp.io' }
]; fallbacks we should be able to switch between by clicking a rotate button that will select next in line and rotate all the way to the first if the user keeps clicking the rotate icon butotn
// Global variables
var player_asset = {};
var wax_endpoint = 'https://atomic.hivebp.io';
var wax_api = wax_endpoint + '/atomicassets/v1/assets?collection_name=harrietsteas&schema_name=';
var wax_api2 = '&page=1&limit=100&order=desc&sort=asset_id';
var myNFTS = {};
var myCollab = {};
var iterator = 0;
var currentCollectionDisplayed = 1;
let showingCover = false;
const coverImagePath = 'images/cover-image.png';

// Initialize on document load
$(document).ready(function() {
    $("#collectionSelected")[0].selectedIndex = 0;
    $('#collectionSelected').hide();
    
    // Setup event listeners
    $('#login_btn').on('click', login);
    $('#toggle-cover').on('click', toggleCover);
    $('#collectionSelected').on('change', setDisplayCollection);
    
    // Initialize age verification
    initAgeVerification();
});

function initAgeVerification() {
    const harrietsId = document.getElementById('harriets');
    if (!localStorage.getItem('ageVerified')) {
        harrietsId.style.display = 'none';
        showAgeModal();
    }
}

function showAgeModal() {
    let modalHTML = `
        <div class="modal fade" id="ageModal" tabindex="-1" role="dialog">
            <div class="modal-dialog scanlines" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Age Verification</h5>   
                    </div>
                    <div class="modal-body">
                        <p>Please verify your date of birth to access the Viewer:</p>
                        <form>
                            <div class="form-group">
                                <input type="text" class="form-control" id="month" placeholder="Month">
                                <input type="text" class="form-control" id="day" placeholder="Day">
                                <input type="text" class="form-control" id="year" placeholder="Year">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="confirmAge()">Submit</button>
                    </div>
                </div>
            </div>
        </div>`;
        
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('ageModal'), {
        backdrop: 'static',
        keyboard: false
    });
    modal.show();
}

function confirmAge() {
    localStorage.setItem('ageVerified', 'true');
    const harrietsId = document.getElementById('harriets');
    harrietsId.style.display = 'block';
    const modal = bootstrap.Modal.getInstance(document.getElementById('ageModal'));
    modal.hide();
    document.getElementById('ageModal').remove();
}

function toggleCover() {
    showingCover = !showingCover;
    const eyeIcon = document.querySelector('#toggle-cover i');
    eyeIcon.className = showingCover ? 'fas fa-eye-slash' : 'fas fa-eye';
    
    const thumbnails = document.querySelectorAll('.mini_thumbnail');
    thumbnails.forEach(img => {
        const originalSrc = img.getAttribute('data-original-src');
        if (showingCover) {
            if (!originalSrc) {
                img.setAttribute('data-original-src', img.src);
            }
            img.src = coverImagePath;
        } else {
            if (originalSrc) {
                img.src = originalSrc;
            }
        }
    });
}

async function displayNFT(data) {
    var asset = data[iterator];
    var img_url = 'https://ipfs.io/ipfs/' + asset.data.img;
    $('#nft-title').text(asset.name);
    $('#nft-details').text('#' + asset.template_mint);
    $('#selected-nft').attr('src', img_url);
}

function insertThumbnail(intoDiv, imgSrc, imgID, num, name, templateMint) {
    const colDiv = document.createElement('div');
    colDiv.className = 'col';

    const cardDiv = document.createElement('div');
    cardDiv.className = 'card h-100';

    const img = document.createElement('img');
    img.src = showingCover ? coverImagePath : imgSrc;
    img.className = 'card-img-top mini_thumbnail';
    img.id = num;
    img.setAttribute('data-asset-id', imgID);
    if (showingCover) {
        img.setAttribute('data-original-src', imgSrc);
    }

    img.onclick = function() {
        iterator = Number(this.id);
        displayNFT(myNFTS);
    };

    const cardBody = document.createElement('div');
    cardBody.className = 'card-body';

    const cardTitle = document.createElement('h5');
    cardTitle.className = 'card-title';
    cardTitle.textContent = name || `NFT #${num}`;

    const cardText = document.createElement('p');
    cardText.className = 'card-text';
    cardText.textContent = templateMint ? `Mint #${templateMint}` : '';

    cardBody.appendChild(cardTitle);
    cardBody.appendChild(cardText);
    cardDiv.appendChild(img);
    cardDiv.appendChild(cardBody);
    colDiv.appendChild(cardDiv);
    
    document.querySelector(intoDiv).appendChild(colDiv);
}

async function displayInventoryThumbnails(intoDiv, data) {
    const container = document.querySelector(intoDiv);
    container.innerHTML = '';
    for (let a in data) {
        const asset = data[a];
        const img_url = 'https://ipfs.io/ipfs/' + asset.data.img;
        insertThumbnail(intoDiv, img_url, asset.asset_id, a, asset.name, asset.template_mint);
    }
}

// Handle keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft' && iterator > 0) {
        iterator--;
        displayNFT(myNFTS);
    } else if (e.key === 'ArrowRight' && iterator < myNFTS.length - 1) {
        iterator++;
        displayNFT(myNFTS);
    }
});

async function fetchTable(user, collection, schema) {
    var promises = [];
    var result = [];
    var finalResult = [];
    var currentPage = 1;
    var maxPages = 5;
    while (currentPage <= maxPages) {
        const url = 'https://wax.api.atomicassets.io/atomicassets/v1/assets?collection_name=' + collection + '&schema_name=' + schema + '&owner=' + user + '&page=' + currentPage + '&limit=100&order=desc&sort=asset_id';
        promises.push(await axios.get(url));
        currentPage++;
    }
    const data = await Promise.all(promises);
    data.forEach(({data}) => {
        for (n in data) {
            if (data[n].length) {
                result = [...result, data[n]];
            }
        }
        finalResult = [].concat.apply([], result);
    });
    return finalResult;
}

async function loadCollections(user, collection, schema) {  
    myNFTS = await fetchTable(user, collection, schema);
    await changeNFT('next', myNFTS); 
    await displayInventoryThumbnails('#action_thumbs', myNFTS);  
    $('#logotext span').text(`Discovered ${myNFTS.length} ${schema} NFTs in ${user}'s wallet.`);
}

async function changeNFT(direction, assets) {
    if (direction === 'next') {
        iterator++;
    } else if (direction === 'back') {
        iterator--;
    }
    if (iterator >= myNFTS.length) {
        iterator = 0;
    }
    await displayNFT(myNFTS);
}

function setDisplayCollection() {
    const selected = $('#collectionSelected').val();
    switch(selected) {
        case '1':
            loadCollections(myUser, 'harrietsteas', 'collectibles');
            break;
        case '2':
            loadCollections(myUser, 'pseudaistats', 'research');
            break;
        case '3':
            loadCollections(myUser, 'pseudaistats', 'series');
            break;
    }
}

function addText(text) {
  document.getElementById("data").innerHTML += text;
}

async function loadBlockchainData(url) {
  let response = await fetch(url);
  let data = await response.json();
  let result = data.data;
  return result;
}

function clearCurrentImage(){
  $('#action_h1').empty();
  $('#action_thumbs').empty();
}

function changeThumbnailSize(){
  if(thumb_size<=3){ 
  var size = [32, 92,164,256];
  $(".mini_thumbnail").css("width", size[thumb_size] + "px"); 
  thumb_size++; 
  } else {
    thumb_size=0;
  }
}

function showPlayer(){
  $('#player').show();
}