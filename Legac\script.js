var player_asset = {};
var wax_endpoint = 'https://api.wax-aa.bountyblok.io';
var wax_api = wax_endpoint + '/atomicassets/v1/assets?collection_name=harrietsteas&schema_name=';
var wax_api2 = '&page=1&limit=100&order=desc&sort=asset_id';
var myNFTS = {};
var myCollab = {};
var iterator = 0;
var currentCollectionDisplayed=1;

function addText(text) {
  document.getElementById("data").innerHTML += text;
}

async function fetchTable(user, collection, schema) {
  var promises = [];
  var result = [];
  var finalResult = [];
  var currentPage = 1;
  var maxPages = 5; // increase as needed during testings
  while (currentPage <= maxPages) {
    const url = 'https://wax.api.atomicassets.io/atomicassets/v1/assets?collection_name=' + collection + '&schema_name=' + schema + '&owner=' + user + '&page=' + currentPage + '&limit=100&order=desc&sort=asset_id';
    promises.push(await axios.get(url));
    currentPage++;
  }
  const data = await Promise.all(promises);
  data.forEach(({
    data
  }) => {
    for (n in data) {
      if (data[n].length) {
        result = [...result, data[n]];
      }
    }
    finalResult = [].concat.apply([], result);
  });
  storeData(schema, finalResult); // store the data for vehicles and creatures for img calls
  return finalResult;
}


async function loadBlockchainData(url) {
  let response = await fetch(url);
  let data = await response.json();
  let result = data.data;
  return result;
}

async function changeNFT(direction, assets) {
  if (direction == 'next') {
    iterator++;
  } else if (direction == 'back') {
    iterator--;
  }
  displayNFT(myNFTS);
  if (iterator > myNFTS.length - 1) {
    iterator = 0;
    displayNFT(myNFTS);
  }
}

async function displayNFT(data) {
  var asset = data[iterator];
  var img_url = 'https://ipfs.io/ipfs/' + asset.data.img;
  var style = 'photo-image'; 
  $('#action_h1').text(asset.name + '(#' + asset.template_mint + ')');  
  $("<img class='img_nft scale-in-center' src=" + img_url + "  >").prependTo("#action_h1");
}



async function loadCollections(user, collection, schema) {  
  myNFTS = await fetchTable(user, collection, schema)
  var dispResults = await changeNFT('next', myNFTS); 
  var dispThumbnailLibrary = await displayInventoryThumbnails('#action_thumbs', myNFTS);  
  $('#logotext').text("Discovered " + myNFTS.length + " " + schema + " NFTs in " + myUser + '\s wallet.');
}

async function displayInventoryThumbnails(intoDiv, data) {
  for (a in data) {
    var asset = data[a];
    var img_url = 'https://ipfs.io/ipfs/' + asset.data.img;
    insertThumbnail(intoDiv, img_url, asset.asset_id, a);
  }
}

function insertThumbnail(intoDiv, imgSrc, imgID, num) {
  var img = document.createElement("img");
  img.setAttribute("src", imgSrc);
  img.setAttribute("id", num);
  img.setAttribute("data-asset-id", imgID);
  img.setAttribute("class", "mini_thumbnail"); 
  img.onclick = function() {
    iterator = Number(this.id);
    displayNFT(myNFTS);
  }; 
  $(img).appendTo(intoDiv); 
}

var thumb_size=0;
function changeThumbnailSize(){
  if(thumb_size<=3){ 
  var size = [32, 92,164,256];
  $(".mini_thumbnail").css("width", size[thumb_size] + "px"); 
  thumb_size++; 
  } else {
    thumb_size=0;
  }
}

function clearCurrentImage(){
  $('#action_h1').empty();
  $('#action_thumbs').empty();
}
 
function setDisplayCollection(){
  
  var c = Number($('#collectionSelected option:selected').val());
  
  if(c==1){
  $('#logotext').text('Loading in progress.');
  currentCollectionDisplayed=1;
  loadCollections(myUser, 'harrietsteas', 'collectibles');
  }
  
  if(c==2){
  $('#logotext').text('Loading in progress.');
  currentCollectionDisplayed=2;
  loadCollections(myUser, 'pseudaimusic', 'research');
  }
  
  if(c==3){
  $('#logotext').text('Loading in progress.');
  currentCollectionDisplayed=3;
  loadCollections(myUser, 'pseudaimusic', 'series');
  }
  
}

function showPlayer(){
  $('#player').show();
}

// Create modal
function showAgeModal() {
let modalHTML = `
  <div class="modal fade" id="ageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog scanlines" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Age Verification</h5>   
        </div>
        <div class="modal-body">
          <p>Please verify your date of birth to access the Viewer:</p>
          <form>
            <div class="form-group">
              <input type="text" class="form-control" id="month" placeholder="Month">
              
              <input type="text" class="form-control" id="day" placeholder="Day">
              
              <input type="text" class="form-control" id="year" placeholder="Year">
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn green_btn" onclick="confirmAge()">Confirm</button> 
        </div>
      </div>
    </div>
  </div>
`;

document.body.insertAdjacentHTML('beforeend', modalHTML);
const currentYear = new Date().getFullYear(); 
const minBirthYear = currentYear - 19; 
const randomMonth = Math.floor(Math.random() * 12) + 1;
const randomDay = Math.floor(Math.random() * 28) + 1; 
document.getElementById('month').value = randomMonth;
document.getElementById('day').value = randomDay; 
document.getElementById('year').value = minBirthYear;   
}
 
const harrietsId = document.getElementById('harriets');
harrietsId.style.display = 'none';

function confirmAge() {      
  $('#ageModal').remove();   
  harrietsId.style.display = 'block'; 
  $('#action_h1').text='';
}
 
showAgeModal();