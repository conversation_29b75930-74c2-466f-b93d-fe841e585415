@import url(https://fonts.googleapis.com/css?family=Exo+2:200i); 
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');

body {
	background: black;
	overflow-x: hidden;
	//font-family: 'Exo 2', sans-serif; 
  font-family: 'Orbitron', sans-serif;
}

:root { 
	--neon-text-color: #31c16a;
	--neon-border-color: #08fa0f;
}
h1, h2, h3 {
  font-weight: 800;
  margin-bottom: 1rem;
}
.container { 
	border: 1px solid #31c16a; 
}

.logo {
	padding: 16px;
	margin: 8px; 
	font-size:16px;  
  color: #31c16a;
}

#running_text {
	font-size: 14px;  
}

.inventory {
	display: flex;
	flex-direction: row;
	height: 200px;
	text-transform: uppercase;
	font-family: Arial;
	width: 90%;
	margin: 8px; 
}

.view {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap; 
	width: 90%;
	text-transform: uppercase; 
}

.scroller {
	overflow-y: scroll;
	scrollbar-color: #31c16a #12211A;
	scrollbar-width: thin;
}

#data {
	color: black;
	font-size: 12px;
	background: black;
	flex-wrap: wrap;
	flex-direction: row;
}

#data2 {
	color: black;
	font-size: 12px;
	background: black;
	flex-wrap: wrap;
	flex-direction: row;
}

.tab {
	padding: 8px;
	width: 300px;
	margin: 8px;
	border: 3px solid #31c16a;  
	font-size: 14px; 
	color: #08fa0f;
	background: #12211a;
}

.status {
	font-size: 14px; 
	color: #08fa0f;
	background: #12211a;
	width: 25%;
}

.menu {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap; 
  border-top: 1px solid #31c16a;  
}

.img_nft {
	display: flex;
	width: 30em;
} 

.btn {
  padding: 8px;
  margin: 6px;
  min-width: 10em;
  border: 3px solid #31c16a;
  font-size: 14px; 
}

.green_btn {
	border: 1px solid #31c16a;    
  padding: 0.5rem 1rem;
  background: none;
  color: #31c16a;
  font-size: 1rem;
  transition: all 0.2s;
}

.green_btn:hover { 
	color: black;
	background: #31c16a;
}

.canvas {
	padding-left: 0;
	padding-right: 0;
	margin-left: auto;
	margin-right: auto;
	margin: 0.5em;
  
}

.action_text {
	overflow-y: scroll;
	padding: 16px;
	margin: 8px;
	color: #08fa0f;
	background: none;
}

.action_text h1 {
	color: #08fa0f; 
	font-size: 18px;
}

.action_text p {
	color: #08fa0f; 
	font-size: 14px;
}

.action_text a {
	color: #08fa0f; 
	font-size: 14px;
}


.footer {
	color: #31c16a;
  height: 50px;
  position: absolute;
  bottom: 0;
  width: 100%; 
}

.footer-content { 
  text-align: center;
  padding-top: 15px; 
}

.orb_tab {
	background: #020204;
}

.next {
	background: yellow;
}

.blink {
	background-color: black;
	animation: blink-animation 1s steps(5, start) infinite;
	-webkit-animation: blink-animation 1s steps(5, start) infinite;
}


#action_thumbs{
  margin-bottom:3em;
}
.mini_thumbnail {
	width: 32px;
	padding: 12px;
	border: 1px solid #31c16a; 
}

.mini_thumbnail:hover {
	-webkit-animation: blink-2 0.4s both;
	animation: blink-2 0.4s both;
	background: none;
	border: 1px solid white; 
}

#ageModal {
  display: flex;
  align-items: center;
  justify-content: center;
}
 
.modal-content {
  padding:1em;
  background: #0d0d0d;
  border: 1px solid #31c16a; 
  color: #31c16a;
}

.modal-header, 
.modal-footer {
  border: none;
}

.modal-title {
  font-weight: bold;
  font-size: 24px;
}

/* Form Styling */
.form-control {
  background-color: #222;
  border-color: #31c16a;
  color: #31c16a;
  width:8em;
  padding:0.5em;
  margin:0.2em;
}

.form-control:focus {
  box-shadow: none;
}
.form-control::selection {
  background: #31c16a;
  color: #222;
} 
.form-control:active {
  border-color: #31c16a;
}
 
.form-control:focus {
  border-color: #0d0d0d;
}
 
.form-control:active {
  border-color: #31c16a;
}
 
.scanlines {
  animation: scanline 5s linear infinite;
}

@keyframes scanline {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 100%; 
  }
}

.scanlines {
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 1px,
    rgba(255,255,255,0.05) 1px,
    rgba(255,255,255,0.05) 2px
  );
  background-size: 100% 4px;
}



.blink-2 {
	-webkit-animation: blink-2 0.4s both;
	animation: blink-2 0.4s both;
}

@-webkit-keyframes blink-2 {
	0% {
		opacity: 1;
	}

	50% {
		opacity: 0.2;
	}

	100% {
		opacity: 1;
	}
}

@keyframes blink-2 {
	0% {
		opacity: 1;
	}

	50% {
		opacity: 0.2;
	}

	100% {
		opacity: 1;
	}
}


@keyframes blink-animation {
	to {
		visibility: hidden;
	}
}

@-webkit-keyframes blink-animation {
	to {
		visibility: hidden;
	}
}

.blinking {
	animation: blinker .5s linear infinite;
}

@keyframes blinker {
	50% {
		opacity: 0;
	}
}

.grain {
	position: fixed;
	pointer-events: none;
	-webkit-animation: grainAnimation 1s steps(4) infinite;
	animation: grainAnimation 1s steps(4) infinite;
	background-image: url(https://image.ibb.co/mPMJEH/grain_888ad7615a.png);
	height: 180%;
	left: -40%;
	top: -40%;
	width: 180%;
	z-index: 46;
}

@-webkit-keyframes grainAnimation {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0)
	}

	10% {
		-webkit-transform: translate(-5%, -5%);
		transform: translate(-5%, -5%)
	}

	20% {
		-webkit-transform: translate(-10%, 5%);
		transform: translate(-10%, 5%)
	}

	30% {
		-webkit-transform: translate(5%, -10%);
		transform: translate(5%, -10%)
	}

	40% {
		-webkit-transform: translate(-5%, 15%);
		transform: translate(-5%, 15%)
	}

	50% {
		-webkit-transform: translate(-10%, 5%);
		transform: translate(-10%, 5%)
	}

	60% {
		-webkit-transform: translate(15%, 0);
		transform: translate(15%, 0)
	}

	70% {
		-webkit-transform: translate(0, 10%);
		transform: translate(0, 10%)
	}

	80% {
		-webkit-transform: translate(-15%, 0);
		transform: translate(-15%, 0)
	}

	90% {
		-webkit-transform: translate(10%, 5%);
		transform: translate(10%, 5%)
	}

	100% {
		-webkit-transform: translate(5%, 0);
		transform: translate(5%, 0)
	}
}

@keyframes grainAnimation {
	0% {
		-webkit-transform: translate(0, 0);
		transform: translate(0, 0)
	}

	10% {
		-webkit-transform: translate(-5%, -5%);
		transform: translate(-5%, -5%)
	}

	20% {
		-webkit-transform: translate(-10%, 5%);
		transform: translate(-10%, 5%)
	}

	30% {
		-webkit-transform: translate(5%, -10%);
		transform: translate(5%, -10%)
	}

	40% {
		-webkit-transform: translate(-5%, 15%);
		transform: translate(-5%, 15%)
	}

	50% {
		-webkit-transform: translate(-10%, 5%);
		transform: translate(-10%, 5%)
	}

	60% {
		-webkit-transform: translate(15%, 0);
		transform: translate(15%, 0)
	}

	70% {
		-webkit-transform: translate(0, 10%);
		transform: translate(0, 10%)
	}

	80% {
		-webkit-transform: translate(-15%, 0);
		transform: translate(-15%, 0)
	}

	90% {
		-webkit-transform: translate(10%, 5%);
		transform: translate(10%, 5%)
	}

	100% {
		-webkit-transform: translate(5%, 0);
		transform: translate(5%, 0)
	}
}


/*  if the screen is 600 px or narrower */
@media only screen and (max-width: 600px) {
	.view {
		flex-wrap: wrap;
		width: 100% !important;
	}

	.tab {
		width: 100% !important;
	}

}


/*  FX - flicker */  
/* Animate neon flicker */
@keyframes flicker {

	0%,
	19%,
	21%,
	23%,
	25%,
	54%,
	56%,
	100% {

		text-shadow:
			-0.2rem -0.2rem 1rem #fff,
			0.2rem 0.2rem 1rem #fff,
			0 0 2rem var(--neon-text-color),
			0 0 4rem var(--neon-text-color),
			0 0 6rem var(--neon-text-color),
			0 0 8rem var(--neon-text-color),
			0 0 10rem var(--neon-text-color);

		box-shadow:
			0 0 .5rem #fff,
			inset 0 0 .5rem #fff,
			0 0 2rem var(--neon-border-color),
			inset 0 0 2rem var(--neon-border-color),
			0 0 4rem var(--neon-border-color),
			inset 0 0 4rem var(--neon-border-color);
	}

	20%,
	24%,
	55% {
		text-shadow: none;
		box-shadow: none;
	}
}


.shake {
	-webkit-animation: shake-vertical 8s cubic-bezier(0.455, 0.030, 0.515, 0.955) 0.5s infinite both;
	animation: shake-vertical 8s cubic-bezier(0.455, 0.030, 0.515, 0.955) 0.5s infinite both;
}
 
@-webkit-keyframes shake {

	0%,
	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
	}

	10%,
	30%,
	50%,
	70% {
		-webkit-transform: translateY(-4px);
		transform: translateY(-4px);
	}

	20%,
	40%,
	60% {
		-webkit-transform: translateY(4px);
		transform: translateY(4px);
	}

	80% {
		-webkit-transform: translateY(3.2px);
		transform: translateY(3.2px);
	}

	90% {
		-webkit-transform: translateY(-3.2px);
		transform: translateY(-3.2px);
	}
}

@keyframes shake-vertical {

	0%,
	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
	}

	10%,
	30%,
	50%,
	70% {
		-webkit-transform: translateY(-4x);
		transform: translateY(-4px);
	}

	20%,
	40%,
	60% {
		-webkit-transform: translateY(4px);
		transform: translateY(4px);
	}

	80% {
		-webkit-transform: translateY(3.2px);
		transform: translateY(3.2px);
	}

	90% {
		-webkit-transform: translateY(-3.2px);
		transform: translateY(-3.2px);
	}
}


.scale-in-center {
	-webkit-animation: scale-in-center .15s cubic-bezier(.25, .46, .45, .94) both;
	animation: scale-in-center .15s cubic-bezier(.25, .46, .45, .94) both
}
 
@-webkit-keyframes scale-in-center {
	0% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 1
	}

	100% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1
	}
}

@keyframes scale-in-center {
	0% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 1
	}

	100% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1
	}
}
   
 select {
	 appearance: none;
	 -moz-appearance: none;
	 -webkit-appearance: none;
	 text-indent: 0.01px;
	 text-overflow: '';
	   margin: 6px;    min-width: 20em;	font-size: 14px;
    padding: 10px;
	 border: 1px solid #31c16a;	 
	 background-color: black;
	 color: #31c16a;   
	 outline: none;
	 display: inline-block;
	 background-image: linear-gradient(45deg, transparent 50%, #31c16a 50%), linear-gradient(135deg, #31c16a 50%, transparent 50%), linear-gradient(to right, #000000, #000000), linear-gradient(to right, #31c16a, #31c16a);
	 background-position: calc(100% - 21px) 50%, calc(100% - 13px) 50%, calc(100% - 3px) 50%, calc(100% - 2px) 50%;
	 background-size: 8px 8px, 8px 8px, 36px calc(100% - 6px), 38px calc(100% - 4px);
	 background-repeat: no-repeat;
}
 @media screen and (-webkit-min-device-pixel-ratio: 0) {
	 select {
		 padding-right: 3em;
	}
}
 select::-ms-expand {
	 display: none;
}  
 