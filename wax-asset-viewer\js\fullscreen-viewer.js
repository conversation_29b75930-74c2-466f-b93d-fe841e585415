// Full Screen Image Viewer functionality
var FullscreenViewer = {
    isOpen: false,
    zoomLevel: 1, // 0.5 = zoomed out, 1 = fit screen, 2 = zoomed in
    currentAssets: [],
    currentIndex: 0,
    isBlurred: true, // Default to blurred for <PERSON>'s Teas collection
    
    // Initialize the fullscreen viewer
    initialize: function() {
        this.bindEvents();
        this.setupBlurToggle();
    },
    
    // Bind all event listeners
    bindEvents: function() {
        var viewer = document.getElementById('fullscreen-viewer');
        var image = document.getElementById('fullscreen-image');
        var closeBtn = document.getElementById('fullscreen-close');
        var prevBtn = document.getElementById('fullscreen-prev');
        var nextBtn = document.getElementById('fullscreen-next');
        var featuredImage = document.getElementById('featured-image');
        
        // Featured image click to open fullscreen
        if (featuredImage) {
            featuredImage.parentElement.addEventListener('click', this.openFromFeatured.bind(this));
        }
        
        // Close button
        if (closeBtn) {
            closeBtn.addEventListener('click', this.close.bind(this));
        }
        
        // Navigation buttons
        if (prevBtn) {
            prevBtn.addEventListener('click', this.previousImage.bind(this));
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', this.nextImage.bind(this));
        }
        
        // Click on background to close
        if (viewer) {
            viewer.addEventListener('click', function(e) {
                if (e.target === viewer) {
                    this.close();
                }
            }.bind(this));
        }
        
        // Image click to zoom
        if (image) {
            image.addEventListener('click', this.toggleZoom.bind(this));
        }

        // Mouse wheel zoom
        if (viewer) {
            viewer.addEventListener('wheel', this.handleWheel.bind(this), { passive: false });
        }

        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboard.bind(this));
        
        // Thumbnail clicks
        this.bindThumbnailClicks();
    },
    
    // Setup blur toggle functionality
    setupBlurToggle: function() {
        var blurBtn = document.getElementById('blur-toggle-btn');
        if (blurBtn) {
            blurBtn.addEventListener('click', this.toggleBlur.bind(this));
            // Set initial state
            this.updateBlurState();
        }
    },
    
    // Bind thumbnail clicks for fullscreen viewing
    bindThumbnailClicks: function() {
        // This will be called when thumbnails are rendered
        var thumbnails = document.querySelectorAll('.thumbnail-item');
        thumbnails.forEach(function(thumb, index) {
            thumb.addEventListener('dblclick', function() {
                this.openFromThumbnail(index);
            }.bind(this));
        }.bind(this));
    },
    
    // Open fullscreen from featured image
    openFromFeatured: function() {
        if (Gallery.assets && Gallery.assets.length > 0) {
            this.currentAssets = Gallery.assets;
            this.currentIndex = Gallery.currentIndex;
            this.open();
        }
    },
    
    // Open fullscreen from thumbnail
    openFromThumbnail: function(index) {
        if (Gallery.assets && Gallery.assets.length > 0) {
            this.currentAssets = Gallery.assets;
            this.currentIndex = index;
            this.open();
        }
    },
    
    // Open the fullscreen viewer
    open: function() {
        var viewer = document.getElementById('fullscreen-viewer');
        if (viewer && this.currentAssets.length > 0) {
            this.isOpen = true;
            this.zoomLevel = 1; // Reset to fit screen
            this.updateImage();
            this.updateZoomState();
            viewer.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    },

    // Close the fullscreen viewer
    close: function() {
        var viewer = document.getElementById('fullscreen-viewer');
        if (viewer) {
            this.isOpen = false;
            this.zoomLevel = 1; // Reset zoom
            viewer.classList.remove('active');
            document.body.style.overflow = '';

            var image = document.getElementById('fullscreen-image');
            if (image) {
                image.classList.remove('zoomed-in', 'zoomed-out', 'fit-screen');
            }
        }
    },
    
    // Update the displayed image
    updateImage: function() {
        var image = document.getElementById('fullscreen-image');
        if (image && this.currentAssets[this.currentIndex]) {
            var asset = this.currentAssets[this.currentIndex];
            if (asset.data && asset.data.img) {
                image.src = Utils.getImageUrl(asset.data.img);
                image.alt = Utils.formatAssetName(asset);

                // Wait for image to load to determine optimal sizing
                image.onload = function() {
                    this.optimizeImageDisplay(image);
                }.bind(this);
            }
        }
    },

    // Optimize image display based on dimensions
    optimizeImageDisplay: function(image) {
        var aspectRatio = image.naturalWidth / image.naturalHeight;
        var viewportAspectRatio = window.innerWidth / window.innerHeight;

        // Remove previous size classes
        image.classList.remove('tall-image', 'wide-image');

        // Add appropriate class based on image dimensions
        if (aspectRatio < 0.7) {
            // Very tall image
            image.classList.add('tall-image');
        } else if (aspectRatio > 1.5) {
            // Very wide image
            image.classList.add('wide-image');
        }
    },
    
    // Navigate to next image
    nextImage: function() {
        if (this.currentAssets.length > 1) {
            this.currentIndex = (this.currentIndex + 1) % this.currentAssets.length;
            this.zoomLevel = 1; // Reset to fit screen
            this.updateImage();
            this.updateZoomState();
        }
    },

    // Navigate to previous image
    previousImage: function() {
        if (this.currentAssets.length > 1) {
            this.currentIndex = this.currentIndex === 0
                ? this.currentAssets.length - 1
                : this.currentIndex - 1;
            this.zoomLevel = 1; // Reset to fit screen
            this.updateImage();
            this.updateZoomState();
        }
    },
    
    // Cycle through zoom levels: fit screen -> zoom in -> zoom out -> fit screen
    toggleZoom: function() {
        if (this.zoomLevel === 1) {
            this.zoomLevel = 2; // Zoom in
        } else if (this.zoomLevel === 2) {
            this.zoomLevel = 0.5; // Zoom out
        } else {
            this.zoomLevel = 1; // Fit screen
        }
        this.updateZoomState();
    },

    // Update zoom state visual classes
    updateZoomState: function() {
        var image = document.getElementById('fullscreen-image');
        var content = document.querySelector('.fullscreen-content');

        if (image) {
            // Remove all zoom classes
            image.classList.remove('zoomed-in', 'zoomed-out', 'fit-screen');

            // Add appropriate class based on zoom level
            if (this.zoomLevel === 2) {
                image.classList.add('zoomed-in');
                if (content) {
                    content.classList.add('zoomed');
                }
            } else if (this.zoomLevel === 0.5) {
                image.classList.add('zoomed-out');
                if (content) {
                    content.classList.remove('zoomed');
                }
            } else {
                image.classList.add('fit-screen');
                if (content) {
                    content.classList.remove('zoomed');
                }
            }
        }
    },
    
    // Toggle blur for discretion
    toggleBlur: function() {
        this.isBlurred = !this.isBlurred;
        this.updateBlurState();
    },
    
    // Update blur state on UI elements
    updateBlurState: function() {
        var blurBtn = document.getElementById('blur-toggle-btn');
        var gallery = document.getElementById('thumbnail-gallery');
        var featured = document.getElementById('featured-asset');
        
        if (blurBtn) {
            var icon = blurBtn.querySelector('i');
            var text = blurBtn.querySelector('span');
            
            if (this.isBlurred) {
                blurBtn.classList.add('active');
                icon.className = 'fas fa-eye-slash';
                text.textContent = 'Unblur';
            } else {
                blurBtn.classList.remove('active');
                icon.className = 'fas fa-eye';
                text.textContent = 'Blur';
            }
        }
        
        // Only apply blur to Harriet's Teas collection
        if (AppState.collection.name === 'harrietsteas') {
            if (gallery) {
                if (this.isBlurred) {
                    gallery.classList.add('blurred');
                } else {
                    gallery.classList.remove('blurred');
                }
            }
            
            if (featured) {
                if (this.isBlurred) {
                    featured.classList.add('blurred');
                } else {
                    featured.classList.remove('blurred');
                }
            }
        }
    },
    
    // Handle mouse wheel for zooming
    handleWheel: function(event) {
        if (!this.isOpen) return;

        event.preventDefault();

        if (event.deltaY < 0) {
            // Scroll up - zoom in
            if (this.zoomLevel === 0.5) {
                this.zoomLevel = 1;
            } else if (this.zoomLevel === 1) {
                this.zoomLevel = 2;
            }
        } else {
            // Scroll down - zoom out
            if (this.zoomLevel === 2) {
                this.zoomLevel = 1;
            } else if (this.zoomLevel === 1) {
                this.zoomLevel = 0.5;
            }
        }

        this.updateZoomState();
    },

    // Handle keyboard navigation
    handleKeyboard: function(event) {
        if (!this.isOpen) return;

        switch(event.key) {
            case 'Escape':
                event.preventDefault();
                this.close();
                break;
            case 'ArrowLeft':
                event.preventDefault();
                this.previousImage();
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.nextImage();
                break;
            case ' ':
                event.preventDefault();
                this.toggleZoom();
                break;
            case '+':
            case '=':
                event.preventDefault();
                if (this.zoomLevel === 0.5) {
                    this.zoomLevel = 1;
                } else if (this.zoomLevel === 1) {
                    this.zoomLevel = 2;
                }
                this.updateZoomState();
                break;
            case '-':
                event.preventDefault();
                if (this.zoomLevel === 2) {
                    this.zoomLevel = 1;
                } else if (this.zoomLevel === 1) {
                    this.zoomLevel = 0.5;
                }
                this.updateZoomState();
                break;
            case '0':
                event.preventDefault();
                this.zoomLevel = 1; // Reset to fit screen
                this.updateZoomState();
                break;
        }
    },
    
    // Update when gallery changes
    updateAssets: function(assets, currentIndex) {
        this.currentAssets = assets || [];
        this.currentIndex = currentIndex || 0;
        
        // Re-bind thumbnail clicks when thumbnails are updated
        setTimeout(function() {
            this.bindThumbnailClicks();
        }.bind(this), 100);
        
        // Update blur state for new collection
        this.updateBlurState();
    }
};
