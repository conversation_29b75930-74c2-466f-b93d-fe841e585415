// Full Screen Image Viewer functionality
var FullscreenViewer = {
    isOpen: false,
    isZoomed: false,
    currentAssets: [],
    currentIndex: 0,
    isBlurred: true, // Default to blurred for <PERSON>'s Teas collection
    
    // Initialize the fullscreen viewer
    initialize: function() {
        this.bindEvents();
        this.setupBlurToggle();
    },
    
    // Bind all event listeners
    bindEvents: function() {
        var viewer = document.getElementById('fullscreen-viewer');
        var image = document.getElementById('fullscreen-image');
        var closeBtn = document.getElementById('fullscreen-close');
        var prevBtn = document.getElementById('fullscreen-prev');
        var nextBtn = document.getElementById('fullscreen-next');
        var featuredImage = document.getElementById('featured-image');
        
        // Featured image click to open fullscreen
        if (featuredImage) {
            featuredImage.parentElement.addEventListener('click', this.openFromFeatured.bind(this));
        }
        
        // Close button
        if (closeBtn) {
            closeBtn.addEventListener('click', this.close.bind(this));
        }
        
        // Navigation buttons
        if (prevBtn) {
            prevBtn.addEventListener('click', this.previousImage.bind(this));
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', this.nextImage.bind(this));
        }
        
        // Click on background to close
        if (viewer) {
            viewer.addEventListener('click', function(e) {
                if (e.target === viewer) {
                    this.close();
                }
            }.bind(this));
        }
        
        // Image click to zoom
        if (image) {
            image.addEventListener('click', this.toggleZoom.bind(this));
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboard.bind(this));
        
        // Thumbnail clicks
        this.bindThumbnailClicks();
    },
    
    // Setup blur toggle functionality
    setupBlurToggle: function() {
        var blurBtn = document.getElementById('blur-toggle-btn');
        if (blurBtn) {
            blurBtn.addEventListener('click', this.toggleBlur.bind(this));
            // Set initial state
            this.updateBlurState();
        }
    },
    
    // Bind thumbnail clicks for fullscreen viewing
    bindThumbnailClicks: function() {
        // This will be called when thumbnails are rendered
        var thumbnails = document.querySelectorAll('.thumbnail-item');
        thumbnails.forEach(function(thumb, index) {
            thumb.addEventListener('dblclick', function() {
                this.openFromThumbnail(index);
            }.bind(this));
        }.bind(this));
    },
    
    // Open fullscreen from featured image
    openFromFeatured: function() {
        if (Gallery.assets && Gallery.assets.length > 0) {
            this.currentAssets = Gallery.assets;
            this.currentIndex = Gallery.currentIndex;
            this.open();
        }
    },
    
    // Open fullscreen from thumbnail
    openFromThumbnail: function(index) {
        if (Gallery.assets && Gallery.assets.length > 0) {
            this.currentAssets = Gallery.assets;
            this.currentIndex = index;
            this.open();
        }
    },
    
    // Open the fullscreen viewer
    open: function() {
        var viewer = document.getElementById('fullscreen-viewer');
        if (viewer && this.currentAssets.length > 0) {
            this.isOpen = true;
            this.isZoomed = false;
            this.updateImage();
            viewer.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    },
    
    // Close the fullscreen viewer
    close: function() {
        var viewer = document.getElementById('fullscreen-viewer');
        if (viewer) {
            this.isOpen = false;
            this.isZoomed = false;
            viewer.classList.remove('active');
            document.body.style.overflow = '';
            
            var image = document.getElementById('fullscreen-image');
            if (image) {
                image.classList.remove('zoomed');
            }
        }
    },
    
    // Update the displayed image
    updateImage: function() {
        var image = document.getElementById('fullscreen-image');
        if (image && this.currentAssets[this.currentIndex]) {
            var asset = this.currentAssets[this.currentIndex];
            if (asset.data && asset.data.img) {
                image.src = Utils.getImageUrl(asset.data.img);
                image.alt = Utils.formatAssetName(asset);
            }
        }
    },
    
    // Navigate to next image
    nextImage: function() {
        if (this.currentAssets.length > 1) {
            this.currentIndex = (this.currentIndex + 1) % this.currentAssets.length;
            this.isZoomed = false;
            var image = document.getElementById('fullscreen-image');
            if (image) {
                image.classList.remove('zoomed');
            }
            this.updateImage();
        }
    },
    
    // Navigate to previous image
    previousImage: function() {
        if (this.currentAssets.length > 1) {
            this.currentIndex = this.currentIndex === 0 
                ? this.currentAssets.length - 1 
                : this.currentIndex - 1;
            this.isZoomed = false;
            var image = document.getElementById('fullscreen-image');
            if (image) {
                image.classList.remove('zoomed');
            }
            this.updateImage();
        }
    },
    
    // Toggle zoom on image
    toggleZoom: function() {
        var image = document.getElementById('fullscreen-image');
        if (image) {
            this.isZoomed = !this.isZoomed;
            if (this.isZoomed) {
                image.classList.add('zoomed');
            } else {
                image.classList.remove('zoomed');
            }
        }
    },
    
    // Toggle blur for discretion
    toggleBlur: function() {
        this.isBlurred = !this.isBlurred;
        this.updateBlurState();
    },
    
    // Update blur state on UI elements
    updateBlurState: function() {
        var blurBtn = document.getElementById('blur-toggle-btn');
        var gallery = document.getElementById('thumbnail-gallery');
        var featured = document.getElementById('featured-asset');
        
        if (blurBtn) {
            var icon = blurBtn.querySelector('i');
            var text = blurBtn.querySelector('span');
            
            if (this.isBlurred) {
                blurBtn.classList.add('active');
                icon.className = 'fas fa-eye-slash';
                text.textContent = 'Unblur';
            } else {
                blurBtn.classList.remove('active');
                icon.className = 'fas fa-eye';
                text.textContent = 'Blur';
            }
        }
        
        // Only apply blur to Harriet's Teas collection
        if (AppState.collection.name === 'harrietsteas') {
            if (gallery) {
                if (this.isBlurred) {
                    gallery.classList.add('blurred');
                } else {
                    gallery.classList.remove('blurred');
                }
            }
            
            if (featured) {
                if (this.isBlurred) {
                    featured.classList.add('blurred');
                } else {
                    featured.classList.remove('blurred');
                }
            }
        }
    },
    
    // Handle keyboard navigation
    handleKeyboard: function(event) {
        if (!this.isOpen) return;
        
        switch(event.key) {
            case 'Escape':
                event.preventDefault();
                this.close();
                break;
            case 'ArrowLeft':
                event.preventDefault();
                this.previousImage();
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.nextImage();
                break;
            case ' ':
                event.preventDefault();
                this.toggleZoom();
                break;
        }
    },
    
    // Update when gallery changes
    updateAssets: function(assets, currentIndex) {
        this.currentAssets = assets || [];
        this.currentIndex = currentIndex || 0;
        
        // Re-bind thumbnail clicks when thumbnails are updated
        setTimeout(function() {
            this.bindThumbnailClicks();
        }.bind(this), 100);
        
        // Update blur state for new collection
        this.updateBlurState();
    }
};
