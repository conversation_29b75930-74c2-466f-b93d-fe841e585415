/* Main Styles for WAX Asset Viewer */

:root {
    --neon-green: #31c16a;
    --neon-green-bright: #08fa0f;
    --dark-bg: #000000;
    --dark-surface: #0d0d0d;
    --dark-border: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --border-radius: 12px;
    --transition: all 0.3s ease;
    --shadow-glow: 0 0 20px rgba(49, 193, 106, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.neuromorphic-box {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--neon-green);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--dark-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid var(--dark-border);
    border-top: 3px solid var(--neon-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-text {
    color: var(--neon-green);
    font-size: 1.1rem;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* App Container */
.app-container {
    min-height: 100vh;
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Header */
.app-header {
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 1px solid var(--neon-green);
    box-shadow: 0 0 10px rgba(49, 193, 106, 0.3);
}
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.logo-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.app-title {
    font-size: 2rem;
    font-weight: 800;
    color: var(--neon-green);
    text-shadow: 0 0 10px var(--neon-green);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.status-indicator {
    font-size: 0.8rem;
    color: #ff4444;
}

.status-indicator.connected {
    color: var(--neon-green);
}

/* Endpoint Selector */
.endpoint-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.endpoint-dropdown {
    background: var(--dark-surface);
    border: 1px solid var(--neon-green);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 6px;
    font-family: inherit;
    font-size: 0.9rem;
    outline: none;
    transition: var(--transition);
}

.endpoint-dropdown:focus {
    box-shadow: var(--shadow-glow);
}

.rotate-btn {
    background: none;
    border: 1px solid var(--neon-green);
    color: var(--neon-green);
    padding: 8px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.rotate-btn:hover {
    background: var(--neon-green);
    color: var(--dark-bg);
    transform: rotate(180deg);
}

/* Login Section */
.login-section {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.login-container {
    padding: 40px;
    text-align: center;
    max-width: 400px;
    width: 100%;
}

.login-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--neon-green);
    margin-bottom: 15px;
}

.login-description {
    color: var(--text-secondary);
    margin-bottom: 30px;
    font-size: 0.95rem;
}

.login-button {
    background: none;
    border: 2px solid var(--neon-green);
    color: var(--neon-green);
    padding: 15px 30px;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.login-button:hover {
    background: var(--neon-green);
    color: var(--dark-bg);
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
}

/* Schema Section */
.schema-section {
    margin-bottom: 30px;
}

.schema-container {
    padding: 25px;
}

.schema-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--neon-green);
    margin-bottom: 20px;
    text-align: center;
}

.schema-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.schema-btn {
    background: none;
    border: 1px solid var(--neon-green);
    color: var(--neon-green);
    padding: 20px;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.schema-btn:hover {
    background: rgba(49, 193, 106, 0.1);
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
}

.schema-btn.active {
    background: var(--neon-green);
    color: var(--dark-bg);
}

.schema-btn i {
    font-size: 1.5rem;
}

/* Gallery Section */
.gallery-section {
    margin-top: 30px;
}

.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.gallery-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--neon-green);
}

.gallery-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    background: none;
    border: 1px solid var(--neon-green);
    color: var(--neon-green);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
}

.control-btn:hover {
    background: var(--neon-green);
    color: var(--dark-bg);
}

.asset-count {
    margin-bottom: 20px;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Featured Asset */
.featured-asset {
    padding: 25px;
    margin-bottom: 30px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: center;
}

.featured-image-container {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--dark-surface);
}

.featured-image {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: contain;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.featured-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.featured-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--neon-green);
}

.featured-details {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
}

.featured-controls {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.nav-btn {
    background: none;
    border: 1px solid var(--neon-green);
    color: var(--neon-green);
    padding: 12px 16px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: var(--neon-green);
    color: var(--dark-bg);
    transform: scale(1.1);
}

/* Thumbnail Gallery */
.thumbnail-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    padding: 20px 0;
}

.thumbnail-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid transparent;
}

.thumbnail-item:hover {
    border-color: var(--neon-green);
    box-shadow: var(--shadow-glow);
    transform: translateY(-3px);
}

.thumbnail-item.active {
    border-color: var(--neon-green-bright);
    box-shadow: 0 0 15px var(--neon-green-bright);
}

.thumbnail-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.thumbnail-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 10px 8px 8px;
    color: var(--text-primary);
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

/* Thumbnail Size Variations */
.thumbnail-gallery.size-small {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
}

.thumbnail-gallery.size-small .thumbnail-image {
    height: 80px;
}

.thumbnail-gallery.size-medium {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}

.thumbnail-gallery.size-medium .thumbnail-image {
    height: 150px;
}

.thumbnail-gallery.size-large {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.thumbnail-gallery.size-large .thumbnail-image {
    height: 200px;
}

/* Full Screen Image Viewer */
.fullscreen-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.fullscreen-viewer.active {
    opacity: 1;
    visibility: visible;
}

.fullscreen-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.fullscreen-image {
    max-width: calc(90vw - 100px); /* Account for navigation buttons */
    max-height: calc(90vh - 100px); /* Account for controls */
    width: auto;
    height: auto;
    object-fit: contain;
    border: 2px solid var(--neon-green);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-glow);
    transition: transform 0.3s ease;
    cursor: zoom-in;
    transform-origin: center center;
    display: block;
}

.fullscreen-image.zoomed-in {
    cursor: zoom-out;
    transform: scale(2);
    max-width: none;
    max-height: none;
}

.fullscreen-image.zoomed-out {
    cursor: zoom-in;
    transform: scale(0.5);
}

.fullscreen-image.fit-screen {
    cursor: zoom-in;
    transform: scale(1);
}

/* Ensure zoomed images can be scrolled if they exceed viewport */
.fullscreen-content.zoomed {
    overflow: auto;
    cursor: grab;
}

.fullscreen-content.zoomed:active {
    cursor: grabbing;
}

/* Handle very tall or very wide images */
.fullscreen-image.tall-image {
    max-height: calc(90vh - 100px);
    width: auto;
}

.fullscreen-image.wide-image {
    max-width: calc(90vw - 100px);
    height: auto;
}

.fullscreen-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
}

.fullscreen-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid var(--neon-green);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--neon-green);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.fullscreen-nav:hover {
    background: rgba(49, 193, 106, 0.2);
    box-shadow: var(--shadow-glow);
    transform: translateY(-50%) scale(1.1);
}

.fullscreen-nav.prev {
    left: 20px;
}

.fullscreen-nav.next {
    right: 20px;
}

.fullscreen-close {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid var(--neon-green);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--neon-green);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.fullscreen-close:hover {
    background: rgba(255, 0, 0, 0.3);
    border-color: #ff4444;
    color: #ff4444;
    transform: scale(1.1);
}

/* Featured Image Hover Effect */
.featured-image-container {
    position: relative;
    cursor: pointer;
}

.featured-image-container::after {
    content: "view full screen";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: var(--neon-green);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    border: 1px solid var(--neon-green);
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
    backdrop-filter: blur(10px);
}

.featured-image-container:hover::after {
    opacity: 1;
}

.featured-image-container:hover .featured-image {
    filter: brightness(0.7);
}

/* Blur Toggle Button */
.blur-toggle-btn {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid var(--neon-green);
    border-radius: var(--border-radius);
    color: var(--neon-green);
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.blur-toggle-btn:hover {
    background: rgba(49, 193, 106, 0.2);
    box-shadow: var(--shadow-glow);
    transform: scale(1.05);
}

.blur-toggle-btn.active {
    background: rgba(49, 193, 106, 0.3);
    box-shadow: var(--shadow-glow);
}

/* Blurred Thumbnails */
.thumbnail-gallery.blurred .thumbnail-image {
    filter: blur(8px) brightness(0.5);
    transition: filter 0.3s ease;
}

.thumbnail-gallery.blurred .thumbnail-item:hover .thumbnail-image {
    filter: blur(4px) brightness(0.7);
}

.featured-asset.blurred .featured-image {
    filter: blur(8px) brightness(0.5);
    transition: filter 0.3s ease;
}

.featured-asset.blurred:hover .featured-image {
    filter: blur(4px) brightness(0.7);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.pulse {
    animation: pulse 2s infinite;
}
