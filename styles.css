   body {
      background-color: black; /* Set the background to black */
      margin: 0;
      padding: 0;
      overflow-x: hidden; /* Only hide horizontal overflow */
    }

    .neuromorphic-box {
      background-color: rgba(255, 255, 255, 0.1); /* Slightly transparent white */
      box-shadow: 10px 10px 20px rgba(0, 0, 0, 0.2), -10px -10px 20px rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      backdrop-filter: blur(10px); /* Subtle blur for depth */
    }

    .w-full.h-full.relative {
      display: flex;
      justify-content: center; /* Center horizontally */
      align-items: center; /* Center vertically */
      position: relative;
      height: 100vh; /* Full viewport height */
    }
	
	.img{
	  border-radius:12px;
    }

    .screen-image img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      transition: opacity 0.5s ease-in-out; /* Smooth transition for opacity */
      opacity: 1; /* Default opacity */  
	}
	 
    .screen-image img.fade-out {
      opacity: 0; /* Fade out effect */
    }

    .blur-background {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      filter: blur(50px); /* High blur effect */
      z-index: 1;
      opacity: 0.7; /* Slightly transparent */
    }

    .content {
      position: relative;
      z-index: 2;
    }

    /* Speech Bubble Positioning */
    .speech-container {
      position: absolute;
      bottom: 5%; /* Adjust this value to position it slightly over the image */
      left: 66%; /* Center horizontally */
      transform: translateX(-66%); /* Center horizontally */
      width: 80%; /* Responsive width */
      max-width: 600px; /* Maximum width */
      z-index: 3; /* Ensure it's above the image */
      text-align: center; /* Center text */
    }

    @media (max-width: 768px) {
      .speech-container {
        bottom: 10%; /* Adjust for smaller screens */
        width: 90%; /* Wider on smaller screens */
      }
    }

    @media (max-width: 480px) {
      .speech-container {
        bottom: 15%; /* Adjust for mobile screens */
        width: 95%; /* Almost full width on mobile */
      }
    }

    /* Button Styling */
    .speech-container button {
      background-color: rgba(255, 255, 255, 0.1); /* Slightly transparent white */
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      padding: 10px 20px;
      border-radius: 10px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.2s ease;
    }

    .speech-container button:hover {
      background-color: rgba(255, 255, 255, 0.2); /* Slightly more opaque on hover */
      transform: scale(1.05); /* Slight scale effect */
    }