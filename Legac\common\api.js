async function fetchTable(user, collection, schema) {
	var promises = [];
	var result = [];
	var finalResult = [];
	var currentPage = 1;
	var maxPages = 5; 
	while (currentPage <= maxPages) {
		const url = 'https://wax.api.atomicassets.io/atomicassets/v1/assets?collection_name=' + collection + '&schema_name=' + schema + '&owner=' + user + '&page=' + currentPage + '&limit=100&order=desc&sort=asset_id';
		promises.push(await axios.get(url));
		currentPage++;
		console.log("loaded page " + currentPage + " of " + schema);
	}
	const data = await Promise.all(promises); 
	data.forEach(({
		data
	}) => {
		for (n in data) {
			if (data[n].length) {
				result = [...result, data[n]];
			}
		} 
		  finalResult = [].concat.apply([], result); 
	}); 
	return finalResult; 
}